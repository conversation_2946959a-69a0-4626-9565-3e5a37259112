import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { HttpClient, HttpClientModule } from "@angular/common/http";
import { PouchdbService } from "../../../service/pouchdb.service";
import { DiagnosisService, Diagnosis } from "../../../service/diagnosis.service";

@Component({
  selector: "app-diagnosis",
  templateUrl: "./diagnosis.page.html",
  styleUrls: ["./diagnosis.page.scss"],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, HttpClientModule],
})
export class DiagnosisPage implements OnInit {
  masterData: any = {};
  diagnosisList: any[] = [];
  chapterList: any[] = [];
  diagnosisCategories: any[] = [];
  diagnosisChapters: any[] = [];

  diagnosisMode: "select" | "chapter" | "manual" = "select";
  selectedDiagnosis: string = "";
  isProvisional: boolean = false;

  // PouchDB stored diagnoses
  diagnoses: Diagnosis[] = [];

  // UI state
  saving = false;
  message = '';
  error = '';
  editingDiagnosis: Diagnosis | null = null;

  constructor(
    private http: HttpClient,
    private objPouchdbService: PouchdbService,
    private diagnosisService: DiagnosisService
  ) {}

  async ngOnInit() {
    console.log('DiagnosisPage ngOnInit called');
    await this.loadMasterData();
    this.loadSavedDiagnoses();
  }

  /**  Load Master Data JSON into PouchDB (if not already saved) */
  private async loadMasterData() {
    try {
      console.log(' Loading master data...');
      this.objPouchdbService.getMasterData().subscribe({
        next: (data) => {
          console.log(' Master Data already exists in PouchDB', data);
          this.processMasterData(data);
        },
        error: (error) => {
          console.log(' Master Data not found → Loading from assets...', error);
          this.http.get('assets/data/RemediNovaAPI.json').subscribe({
            next: (jsonData: any) => {
              console.log(' Loaded JSON from assets:', jsonData);
              this.objPouchdbService.addOrUpdateMasterData(jsonData).subscribe({
                next: () => {
                  console.log(' Master Data saved in PouchDB');
                  this.processMasterData(jsonData);
                },
                error: (saveError) => {
                  console.error(' Error saving to PouchDB:', saveError);
                  // Still try to process the data even if saving fails
                  this.processMasterData(jsonData);
                }
              });
            },
            error: (httpError) => {
              console.error(' Error loading JSON from assets:', httpError);
            }
          });
        },
      });
    } catch (err) {
      console.error(' Error loading master data:', err);
    }
  }

  /** ✅ Extract tblDiagnosisCategory and tblDiagnosisChapter */
  private processMasterData(data: any) {
    this.masterData = data;

    console.log(' Processing master data:', data);
    console.log(' Data keys:', Object.keys(data));

    // Initialize arrays
    this.diagnosisList = [];
    this.chapterList = [];

    // Handle nested data structure - data is wrapped in a 'data' array
    if (data.data && Array.isArray(data.data)) {
      console.log('📦 Found data array with', data.data.length, 'items');
      // Find the diagnosis tables in the data array
      for (const item of data.data) {
        console.log('🔍 Checking item:', Object.keys(item));

        if (item.tbldiagnosiscategory) {
          this.diagnosisList = item.tbldiagnosiscategory.filter(
            (d: any) => d.IsDeleted === "0"
          );
          console.log('✅ Found diagnosis categories:', this.diagnosisList.length);
          console.log('📋 Sample category:', this.diagnosisList[0]);
        }

        if (item.tbldiagnosischapter) {
          this.chapterList = item.tbldiagnosischapter.filter(
            (c: any) => c.isdeleted === "0"
          );
          console.log('✅ Found diagnosis chapters:', this.chapterList.length);
          console.log('📋 Sample chapter:', this.chapterList[0]);
        }
      }
    } else {
      // Direct access (if data is already processed)
      console.log('📦 Direct data access');

      if (this.masterData.tbldiagnosiscategory) {
        this.diagnosisList = this.masterData.tbldiagnosiscategory.filter(
          (d: any) => d.IsDeleted === "0"
        );
        console.log('✅ Found diagnosis categories (direct):', this.diagnosisList.length);
      }

      if (this.masterData.tbldiagnosischapter) {
        this.chapterList = this.masterData.tbldiagnosischapter.filter(
          (c: any) => c.isdeleted === "0"
        );
        console.log('✅ Found diagnosis chapters (direct):', this.chapterList.length);
      }
    }

    console.log('📊 Final diagnosis lists:', {
      diagnosisList: this.diagnosisList.length,
      chapterList: this.chapterList.length
    });

    // Force change detection
    setTimeout(() => {
      console.log(' Triggering change detection');
    }, 100);
  }

  /**
   * Add or update diagnosis
   */
  addDiagnosis(): void {
    if (!this.selectedDiagnosis.trim()) return;

    if (this.saving) return;

    this.saving = true;
    this.message = '';
    this.error = '';

    const diagnosisData: Diagnosis = {
      code: this.generateICDCode(),
      name: this.selectedDiagnosis,
      provisional: this.isProvisional,
      diagnosisMode: this.diagnosisMode
    };

    if (this.editingDiagnosis) {
      // Update existing diagnosis
      console.log('Updating diagnosis:', diagnosisData);

      this.diagnosisService.updateDiagnosis(this.editingDiagnosis._id!, diagnosisData).subscribe({
        next: (response) => {
          console.log('✅ Diagnosis updated successfully:', response);
          this.saving = false;
          this.message = `Diagnosis updated successfully!`;
          this.resetForm();
          this.loadSavedDiagnoses();
        },
        error: (err) => {
          console.error('❌ Error updating diagnosis:', err);
          this.saving = false;
          this.error = `Failed to update diagnosis: ${err.message || err}`;
        }
      });
    } else {
      // Save new diagnosis
      console.log('Saving new diagnosis:', diagnosisData);

      this.diagnosisService.saveDiagnosis(diagnosisData).subscribe({
        next: (response) => {
          console.log('✅ Diagnosis saved successfully:', response);
          this.saving = false;
          this.message = `Diagnosis saved successfully! ID: ${response.id}`;
          this.resetForm();
          this.loadSavedDiagnoses();
        },
        error: (err) => {
          console.error('❌ Error saving diagnosis:', err);
          this.saving = false;
          this.error = `Failed to save diagnosis: ${err.message || err}`;
        }
      });
    }
  }

  /**
   * Edit diagnosis - populate form with existing data
   */
  editDiagnosis(item: Diagnosis): void {
    console.log('Editing diagnosis:', item);
    this.editingDiagnosis = item;
    this.selectedDiagnosis = item.name;
    this.isProvisional = item.provisional;
    this.diagnosisMode = item.diagnosisMode;
    this.message = `Editing: ${item.name}`;
    this.error = '';
  }

  /**
   * Delete diagnosis from PouchDB
   */
  deleteDiagnosis(item: Diagnosis): void {
    if (!item._id) return;

    console.log('Deleting diagnosis:', item);

    this.diagnosisService.deleteDiagnosis(item._id).subscribe({
      next: (response) => {
        console.log('✅ Diagnosis deleted successfully:', response);
        this.message = `Diagnosis "${item.name}" deleted successfully!`;
        this.loadSavedDiagnoses();
      },
      error: (err) => {
        console.error('❌ Error deleting diagnosis:', err);
        this.error = `Failed to delete diagnosis: ${err.message || err}`;
      }
    });
  }

  /**
   * Load all saved diagnoses from PouchDB
   */
  loadSavedDiagnoses(): void {
    console.log('Loading saved diagnoses...');

    this.diagnosisService.getAllDiagnoses().subscribe({
      next: (diagnoses) => {
        this.diagnoses = diagnoses;
        console.log(`✅ Loaded ${diagnoses.length} diagnoses:`, diagnoses);

        // Print detailed data to console for debugging
        if (diagnoses.length > 0) {
          console.log('=== DIAGNOSES DATA ===');
          diagnoses.forEach((diagnosis, index) => {
            console.log(`\n--- Diagnosis ${index + 1} ---`);
            console.log('ID:', diagnosis._id);
            console.log('Code:', diagnosis.code);
            console.log('Name:', diagnosis.name);
            console.log('Provisional:', diagnosis.provisional);
            console.log('Mode:', diagnosis.diagnosisMode);
            console.log('Created At:', diagnosis.createdAt);
          });
          console.log('=== END DIAGNOSES DATA ===\n');
        }
      },
      error: (err) => {
        console.error('❌ Error loading diagnoses:', err);
        this.error = `Failed to load diagnoses: ${err.message || err}`;
      }
    });
  }

  /**
   * Reset form to empty state
   */
  private resetForm(): void {
    this.selectedDiagnosis = '';
    this.isProvisional = false;
    this.diagnosisMode = 'select';
    this.editingDiagnosis = null;
  }

  generateICDCode(): string {
    return "X" + Math.floor(100 + Math.random() * 900); // Mock ICD Code
  }
}
