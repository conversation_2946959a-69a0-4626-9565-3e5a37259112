import { Injectable } from '@angular/core';
import PouchDB from 'pouchdb-browser';
import { Observable, from, throwError } from 'rxjs';
import { map, catchError, tap, switchMap } from 'rxjs/operators';

export interface PatientHistory {
  _id?: string;
  historyOfPresentIllness: string;
  personalHistory: string;
  pastMedicalOrSurgicalHistory: string;
  familyHistory: string;
  currentAndRecentMedications: string;
  medicalAllergies: string;
  otherAllergiesOrSensitivities: string;
  additionalNotes: string;
  physicalExamination: string;
  reviewNote: string;
  createdAt?: string;
  type?: string;
}

// The exact shape stored in PouchDB (metadata + required fields)
export interface StoredPatientHistory extends PatientHistory {
  _id: string; // required here
  _rev?: string;
  type: 'patient-history';
  createdAt: string;
}

@Injectable({ providedIn: 'root' })
export class SaveService {
  private db: PouchDB.Database;

  constructor() {
    this.db = new PouchDB('local-consultan', { adapter: 'idb' });
    console.log('SaveService initialized with database: local-consultan');
  }

  /**
   * Save patient history data to PouchDB
   * @param entry PatientHistory data to save
   * @returns Observable with save response
   */
  savePatientHistory(entry: PatientHistory): Observable<PouchDB.Core.Response> {
    const now = new Date().toISOString();
    const doc: StoredPatientHistory = {
      ...entry,
      createdAt: entry.createdAt || now,
      type: 'patient-history',
      _id: entry._id || `patient-history:${Date.now()}:${Math.random().toString(36).substring(2, 11)}`,
    };

    console.log('Saving patient history data:', doc);

    return from(this.db.put(doc)).pipe(
      tap((response) => {
        console.log('Patient history saved successfully:', response);
        console.log('Saved data:', doc);
      }),
      catchError((e) => {
        console.error('Error saving patient history:', e);
        return throwError(() => this.normalizeError(e));
      })
    );
  }

  /**
   * Get all patient histories from PouchDB
   * @returns Observable with array of patient histories
   */
  getAllPatientHistories(): Observable<PatientHistory[]> {
    return from(
      this.db.allDocs<StoredPatientHistory>({
        include_docs: true,
        descending: true,
        startkey: 'patient-history:\uffff',
        endkey: 'patient-history:'
      })
    ).pipe(
      map((res) => {
        console.log('Raw PouchDB response:', res);

        // Type guard for stored patient history
        const isStoredPatientHistory = (d: any): d is StoredPatientHistory =>
          !!d && d.type === 'patient-history';

        const filtered = res.rows
          .map((r) => r.doc)
          .filter((doc): doc is NonNullable<typeof doc> =>
            doc !== undefined && doc !== null && isStoredPatientHistory(doc)
          );

        console.log('Filtered patient histories:', filtered);

        // Return data without internal PouchDB metadata
        return filtered.map(({ _id, _rev, type, ...rest }) => ({
          _id,
          type,
          ...rest
        } as PatientHistory));
      }),
      tap((histories) => {
        console.log('Retrieved patient histories:', histories);
      }),
      catchError((e) => {
        console.error('Error retrieving patient histories:', e);
        return throwError(() => this.normalizeError(e));
      })
    );
  }

  /**
   * Get a specific patient history by ID
   * @param id Document ID
   * @returns Observable with patient history or null
   */
  getPatientHistoryById(id: string): Observable<PatientHistory | null> {
    return from(this.db.get<StoredPatientHistory>(id)).pipe(
      map((doc) => {
        if (doc && doc.type === 'patient-history') {
          const { _id, _rev, type, ...rest } = doc;
          return { _id, type, ...rest } as PatientHistory;
        }
        return null;
      }),
      catchError((e) => {
        if (e.status === 404) {
          console.log(`Patient history with ID ${id} not found`);
          return from([null]);
        }
        console.error('Error retrieving patient history by ID:', e);
        return throwError(() => this.normalizeError(e));
      })
    );
  }

  /**
   * Delete a patient history by ID
   * @param id Document ID
   * @returns Observable with delete response
   */
  deletePatientHistory(id: string): Observable<PouchDB.Core.Response> {
    return from(this.db.get(id)).pipe(
      switchMap((doc: any) => from(this.db.remove(doc))),
      tap((response: any) => {
        console.log('Patient history deleted successfully:', response);
      }),
      catchError((e) => {
        console.error('Error deleting patient history:', e);
        return throwError(() => this.normalizeError(e));
      })
    );
  }

  /**
   * Get database info for debugging
   * @returns Observable with database info
   */
  getDatabaseInfo(): Observable<any> {
    return from(this.db.info()).pipe(
      tap((info) => {
        console.log('Database info:', info);
      }),
      catchError((e) => {
        console.error('Error getting database info:', e);
        return throwError(() => this.normalizeError(e));
      })
    );
  }

  private normalizeError(err: any): Error {
    if (err && typeof err === 'object') {
      if (err.reason) return new Error(err.reason);
      if (err.message) return new Error(err.message);
      if (err.error) return new Error(err.error);
    }
    return new Error(JSON.stringify(err));
  }
}
