import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: 'home',
    loadComponent: () => import('./home/<USER>').then((m) => m.HomePage),
  },
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full',
  },

  // RemediNova Data Table Routes



  // Existing routes
  {
    path: 'navbar',
    loadComponent: () => import('./pages/navbar/navbar.page').then( m => m.NavbarPage)
  },
  {
    path: 'sidebar',
    loadComponent: () => import('./pages/sidebar/sidebar.page').then( m => m.SidebarPage)
  },
  {
    path: 'patientinfo',
    loadComponent: () => import('./pages/patientinfo/patientinfo.page').then( m => m.PatientinfoPage)
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./pages/dashboard/dashboard.page').then( m => m.DashboardPage)
  },
  {
    path: 'tabs',
    loadComponent: () => import('./pages/scroll-tab-container/tabs/tabs.page').then( m => m.TabsPage)
  },
  {
    path: 'patient-history',
    loadComponent: () => import('./pages/scroll-tab-container/patient-history/patient-history.page').then( m => m.PatientHistoryPage)
  },
];
