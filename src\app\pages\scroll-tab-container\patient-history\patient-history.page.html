<div class="patient-history-section">
  <h3>Patient History</h3>

  <form (ngSubmit)="onSave()" #form="ngForm" class="history-grid">
    <div class="field-block">
      <label>History of Present Illness *</label>
      <textarea
        name="historyOfPresentIllness"
        required
        [(ngModel)]="patientHistory.historyOfPresentIllness"
        placeholder="Text area"
      ></textarea>
    </div>

    <div class="field-block">
      <label>Personal History *</label>
      <textarea
        name="personalHistory"
        required
        [(ngModel)]="patientHistory.personalHistory"
        placeholder="Text area"
      ></textarea>
    </div>

    <div class="field-block">
      <label>Past Medical or Surgical History *</label>
      <textarea
        name="pastMedicalOrSurgicalHistory"
        required
        [(ngModel)]="patientHistory.pastMedicalOrSurgicalHistory"
        placeholder="Text area"
      ></textarea>
    </div>

    <div class="field-block">
      <label>Family History *</label>
      <textarea
        name="familyHistory"
        required
        [(ngModel)]="patientHistory.familyHistory"
        placeholder="Text area"
      ></textarea>
    </div>

    <div class="field-block">
      <label>Current And Recent Medications *</label>
      <textarea
        name="currentAndRecentMedications"
        required
        [(ngModel)]="patientHistory.currentAndRecentMedications"
        placeholder="Text area"
      ></textarea>
    </div>

    <div class="field-block">
      <label>Medical Allergies *</label>
      <textarea
        name="medicalAllergies"
        required
        [(ngModel)]="patientHistory.medicalAllergies"
        placeholder="Text area"
      ></textarea>
    </div>

    <div class="field-block">
      <label>Other Allergies Or Sensitivities *</label>
      <textarea
        name="otherAllergiesOrSensitivities"
        required
        [(ngModel)]="patientHistory.otherAllergiesOrSensitivities"
        placeholder="Text area"
      ></textarea>
    </div>

    <div class="field-block">
      <label>Additional Notes *</label>
      <textarea
        name="additionalNotes"
        required
        [(ngModel)]="patientHistory.additionalNotes"
        placeholder="Text area"
      ></textarea>
    </div>

    <div class="field-block">
      <label>Physical Examination *</label>
      <textarea
        name="physicalExamination"
        required
        [(ngModel)]="patientHistory.physicalExamination"
        placeholder="Text area"
      ></textarea>
    </div>

    <div class="field-block">
      <label>Review Note *</label>
      <textarea
        name="reviewNote"
        required
        [(ngModel)]="patientHistory.reviewNote"
        placeholder="Text area"
      ></textarea>
    </div>

    <div class="actions" style="width:100%; display:flex; justify-content:flex-end; align-items:center; margin-top:20px;">
      <button
        type="submit"
        [disabled]="form.invalid || saving"
        style="font-size:14px; color:#f9fafc; font-weight:600; background:#007AFF; border:none; cursor:pointer; padding:10px 20px; border-radius:8px;"
      >
        {{ saving ? 'Saving...' : 'Add' }}
      </button>
    </div>
  </form>

  <div *ngIf="message" style="margin-top:10px; padding:10px; background:#d4edda; color:#155724; border:1px solid #c3e6cb; border-radius:4px;">
    {{ message }}
  </div>
  <div *ngIf="error" style="margin-top:10px; padding:10px; background:#f8d7da; color:#721c24; border:1px solid #f5c6cb; border-radius:4px;">
    {{ error }}
  </div>
</div>
