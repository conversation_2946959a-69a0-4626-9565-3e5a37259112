# Console Output Examples

When you use the optimized patient history form, you'll see detailed logging in the browser console:

## 1. Page Initialization
```
=== PATIENT HISTORY PAGE LOADED ===
SaveService instance: SaveService {db: PouchDB}
Initial patient history model: {historyOfPresentIllness: "", personalHistory: "", ...}
===================================
SaveService initialized with database: local-consultan
```

## 2. Database Information
```
=== DATABASE INFO ===
{
  "db_name": "local-consultan",
  "doc_count": 3,
  "update_seq": 5,
  "purge_seq": 0,
  "compact_running": false,
  "disk_size": 8192,
  "data_size": 2048,
  "instance_start_time": "1691234567890",
  "disk_format_version": 8,
  "committed_update_seq": 5
}
====================
```

## 3. Saving Data
```
Saving patient history data: {
  historyOfPresentIllness: "Patient presents with chest pain...",
  personalHistory: "Non-smoker, occasional alcohol...",
  // ... all form fields
  createdAt: "2024-08-01T10:30:45.123Z",
  type: "patient-history",
  _id: "patient-history:1691234567890:abc123def"
}

Patient history saved successfully: {
  id: "patient-history:1691234567890:abc123def",
  rev: "1-a1b2c3d4e5f6",
  ok: true
}

=== UPLOADED PATIENT HISTORY DATA ===
Document ID: patient-history:1691234567890:abc123def
Revision: 1-a1b2c3d4e5f6
Data: {historyOfPresentIllness: "Patient presents...", ...}
Timestamp: 2024-08-01T10:30:45.123Z
=====================================
```

## 4. Loading All Data
```
=== ALL SAVED PATIENT HISTORIES ===
Total records: 3
Record 1: {
  _id: "patient-history:1691234567890:abc123def",
  type: "patient-history",
  historyOfPresentIllness: "Patient presents with chest pain...",
  // ... all fields
  createdAt: "2024-08-01T10:30:45.123Z"
}
Record 2: { ... }
Record 3: { ... }
===================================
```

## 5. Test Data Population
```
=== TEST DATA FILLED ===
Form populated with test data: {
  historyOfPresentIllness: "Patient presents with chest pain and shortness of breath for the past 2 days.",
  personalHistory: "Non-smoker, occasional alcohol consumption, regular exercise.",
  // ... all test fields
}
========================
```
