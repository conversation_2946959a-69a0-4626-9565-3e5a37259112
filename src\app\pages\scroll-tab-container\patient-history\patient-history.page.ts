import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { SaveService, PatientHistory } from '../../../service/save.service';

@Component({
  selector: 'app-patient-history',
  templateUrl: './patient-history.page.html',
  styleUrls: ['./patient-history.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class PatientHistoryPage implements OnInit {

  // Form data model
  patientHistory: PatientHistory = {
    historyOfPresentIllness: '',
    personalHistory: '',
    pastMedicalOrSurgicalHistory: '',
    familyHistory: '',
    currentAndRecentMedications: '',
    medicalAllergies: '',
    otherAllergiesOrSensitivities: '',
    additionalNotes: '',
    physicalExamination: '',
    reviewNote: ''
  };

  // UI state
  saving = false;
  message = '';
  error = '';
  savedHistories: PatientHistory[] = [];

  constructor(private saveService: SaveService) {}

  ngOnInit(): void {
    console.log('PatientHistoryPage initialized');
    this.loadSavedHistories();
  }

  /**
   * Save patient history data to PouchDB
   */
  onSave(): void {
    if (this.saving) return;

    this.saving = true;
    this.message = '';
    this.error = '';

    console.log('Attempting to save patient history:', this.patientHistory);

    this.saveService.savePatientHistory(this.patientHistory).subscribe({
      next: (response) => {
        console.log('✅ Patient history saved successfully:', response);
        this.saving = false;
        this.message = `Patient history saved successfully! ID: ${response.id}`;

        // Clear form after successful save
        this.resetForm();

        // Reload saved histories to show the new entry
        this.loadSavedHistories();
      },
      error: (err) => {
        console.error('❌ Error saving patient history:', err);
        this.saving = false;
        this.error = `Failed to save patient history: ${err.message || err}`;
      }
    });
  }

  /**
   * Load all saved patient histories from PouchDB
   */
  loadSavedHistories(): void {
    console.log('Loading saved patient histories...');

    this.saveService.getAllPatientHistories().subscribe({
      next: (histories) => {
        this.savedHistories = histories;
        console.log(`✅ Loaded ${histories.length} patient histories:`, histories);

        // Print detailed data to console for debugging
        if (histories.length > 0) {
          console.log('=== PATIENT HISTORIES DATA ===');
          histories.forEach((history, index) => {
            console.log(`\n--- Patient History ${index + 1} ---`);
            console.log('ID:', history._id);
            console.log('Created At:', history.createdAt);
            console.log('History of Present Illness:', history.historyOfPresentIllness);
            console.log('Personal History:', history.personalHistory);
            console.log('Past Medical/Surgical History:', history.pastMedicalOrSurgicalHistory);
            console.log('Family History:', history.familyHistory);
            console.log('Current Medications:', history.currentAndRecentMedications);
            console.log('Medical Allergies:', history.medicalAllergies);
            console.log('Other Allergies:', history.otherAllergiesOrSensitivities);
            console.log('Additional Notes:', history.additionalNotes);
            console.log('Physical Examination:', history.physicalExamination);
            console.log('Review Note:', history.reviewNote);
          });
          console.log('=== END PATIENT HISTORIES DATA ===\n');
        }
      },
      error: (err) => {
        console.error('❌ Error loading patient histories:', err);
        this.error = `Failed to load patient histories: ${err.message || err}`;
      }
    });
  }

  /**
   * Get database information for debugging
   */
  getDatabaseInfo(): void {
    console.log('Getting database information...');

    this.saveService.getDatabaseInfo().subscribe({
      next: (info) => {
        console.log('✅ Database Info:', info);
        this.message = `Database contains ${info.doc_count} documents`;
      },
      error: (err) => {
        console.error('❌ Error getting database info:', err);
        this.error = `Failed to get database info: ${err.message || err}`;
      }
    });
  }

  /**
   * Fill form with test data for development/testing
   */
  fillTestData(): void {
    console.log('Filling form with test data...');

    this.patientHistory = {
      historyOfPresentIllness: 'Patient presents with chest pain that started 2 hours ago. Pain is described as sharp, radiating to left arm. Associated with shortness of breath and nausea.',
      personalHistory: 'Non-smoker, occasional alcohol consumption (2-3 drinks per week). Regular exercise routine. No known drug use.',
      pastMedicalOrSurgicalHistory: 'Hypertension diagnosed 5 years ago, well controlled with medication. Appendectomy in 2015. No other significant medical or surgical history.',
      familyHistory: 'Father had myocardial infarction at age 65. Mother has diabetes type 2. No family history of cancer or genetic disorders.',
      currentAndRecentMedications: 'Lisinopril 10mg daily for hypertension. Multivitamin daily. Recently completed course of amoxicillin for dental infection (ended 1 week ago).',
      medicalAllergies: 'Penicillin - causes rash and swelling. Sulfa drugs - causes nausea and vomiting.',
      otherAllergiesOrSensitivities: 'Seasonal allergies to pollen. Lactose intolerant. Sensitive to shellfish (mild GI upset).',
      additionalNotes: 'Patient is anxious about symptoms. Lives alone but has good family support. Works as office manager, sedentary job.',
      physicalExamination: 'Vital signs: BP 150/95, HR 88, RR 18, Temp 98.6°F. Alert and oriented. Mild diaphoresis noted. Heart sounds regular, no murmurs. Lungs clear bilaterally.',
      reviewNote: 'Recommend ECG and cardiac enzymes. Consider cardiology consultation. Patient educated on symptoms requiring immediate medical attention.'
    };

    console.log('Test data filled:', this.patientHistory);
    this.message = 'Form filled with test data';
  }

  /**
   * Reset form to empty state
   */
  private resetForm(): void {
    this.patientHistory = {
      historyOfPresentIllness: '',
      personalHistory: '',
      pastMedicalOrSurgicalHistory: '',
      familyHistory: '',
      currentAndRecentMedications: '',
      medicalAllergies: '',
      otherAllergiesOrSensitivities: '',
      additionalNotes: '',
      physicalExamination: '',
      reviewNote: ''
    };
  }
}
