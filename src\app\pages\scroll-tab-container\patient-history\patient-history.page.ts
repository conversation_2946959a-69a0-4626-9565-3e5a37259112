import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { SaveService, PatientHistory } from '../../../service/save.service';

@Component({
  selector: 'app-patient-history',
  templateUrl: './patient-history.page.html',
  styleUrls: ['./patient-history.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class PatientHistoryPage implements OnInit {

  // Form data model
  patientHistory: PatientHistory = {
    historyOfPresentIllness: '',
    personalHistory: '',
    pastMedicalOrSurgicalHistory: '',
    familyHistory: '',
    currentAndRecentMedications: '',
    medicalAllergies: '',
    otherAllergiesOrSensitivities: '',
    additionalNotes: '',
    physicalExamination: '',
    reviewNote: ''
  };

  // UI state
  saving = false;
  message = '';
  error = '';

  constructor(private saveService: SaveService) {}

  ngOnInit(): void {
    console.log('PatientHistoryPage initialized');
  }

  /**
   * Save patient history data to PouchDB
   */
  onSave(): void {
    if (this.saving) return;

    this.saving = true;
    this.message = '';
    this.error = '';

    console.log('Saving patient history data:', this.patientHistory);

    this.saveService.savePatientHistory(this.patientHistory).subscribe({
      next: (response) => {
        console.log('✅ Patient history saved successfully:', response);
        console.log('✅ Saved data:', this.patientHistory);

        this.saving = false;
        this.message = `Patient history saved successfully! ID: ${response.id}`;

        // Clear form after successful save
        this.resetForm();
      },
      error: (err) => {
        console.error('❌ Error saving patient history:', err);
        this.saving = false;
        this.error = `Failed to save patient history: ${err.message || err}`;
      }
    });
  }

  /**
   * Reset form to empty state
   */
  private resetForm(): void {
    this.patientHistory = {
      historyOfPresentIllness: '',
      personalHistory: '',
      pastMedicalOrSurgicalHistory: '',
      familyHistory: '',
      currentAndRecentMedications: '',
      medicalAllergies: '',
      otherAllergiesOrSensitivities: '',
      additionalNotes: '',
      physicalExamination: '',
      reviewNote: ''
    };
  }
}
