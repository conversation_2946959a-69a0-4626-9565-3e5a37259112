import { Injectable } from '@angular/core';
import PouchDB from 'pouchdb-browser';
import { Observable, from, throwError } from 'rxjs';
import { map, catchError, tap, switchMap } from 'rxjs/operators';

export interface Diagnosis {
  _id?: string;
  code: string;
  name: string;
  provisional: boolean;
  diagnosisMode: 'select' | 'chapter' | 'manual';
  createdAt?: string;
  type?: string;
}

// The exact shape stored in PouchDB (metadata + required fields)
export interface StoredDiagnosis extends Diagnosis {
  _id: string; // required here
  _rev?: string;
  type: 'diagnosis';
  createdAt: string;
}

@Injectable({ providedIn: 'root' })
export class DiagnosisService {
  private db: PouchDB.Database;

  constructor() {
    this.db = new PouchDB('local-consultan', { adapter: 'idb' });
    console.log('DiagnosisService initialized with database: local-consultan');
  }

  /**
   * Save diagnosis data to PouchDB
   * @param entry Diagnosis data to save
   * @returns Observable with save response
   */
  saveDiagnosis(entry: Diagnosis): Observable<PouchDB.Core.Response> {
    const now = new Date().toISOString();
    const doc: StoredDiagnosis = {
      ...entry,
      createdAt: entry.createdAt || now,
      type: 'diagnosis',
      _id: entry._id || `diagnosis:${Date.now()}:${Math.random().toString(36).substring(2, 11)}`,
    };

    console.log('Saving diagnosis data:', doc);

    return from(this.db.put(doc)).pipe(
      tap((response) => {
        console.log('✅ Diagnosis saved successfully:', response);
        console.log('✅ Saved data:', doc);
      }),
      catchError((e) => {
        console.error('❌ Error saving diagnosis:', e);
        return throwError(() => this.normalizeError(e));
      })
    );
  }

  /**
   * Get all diagnoses from PouchDB
   * @returns Observable with array of diagnoses
   */
  getAllDiagnoses(): Observable<Diagnosis[]> {
    return from(
      this.db.allDocs<StoredDiagnosis>({
        include_docs: true,
        descending: true,
        startkey: 'diagnosis:\uffff',
        endkey: 'diagnosis:'
      })
    ).pipe(
      map((res) => {
        console.log('Raw PouchDB response:', res);

        // Type guard for stored diagnosis
        const isStoredDiagnosis = (d: any): d is StoredDiagnosis =>
          !!d && d.type === 'diagnosis';

        const filtered = res.rows
          .map((r) => r.doc)
          .filter((doc): doc is NonNullable<typeof doc> =>
            doc !== undefined && doc !== null && isStoredDiagnosis(doc)
          );

        console.log('Filtered diagnoses:', filtered);

        // Return data without internal PouchDB metadata
        return filtered.map(({ _id, _rev, type, ...rest }) => ({
          _id,
          type,
          ...rest
        } as Diagnosis));
      }),
      tap((diagnoses) => {
        console.log('Retrieved diagnoses:', diagnoses);
      }),
      catchError((e) => {
        console.error('Error retrieving diagnoses:', e);
        return throwError(() => this.normalizeError(e));
      })
    );
  }

  /**
   * Get a specific diagnosis by ID
   * @param id Document ID
   * @returns Observable with diagnosis or null
   */
  getDiagnosisById(id: string): Observable<Diagnosis | null> {
    return from(this.db.get<StoredDiagnosis>(id)).pipe(
      map((doc) => {
        if (doc && doc.type === 'diagnosis') {
          const { _id, _rev, type, ...rest } = doc;
          return { _id, type, ...rest } as Diagnosis;
        }
        return null;
      }),
      catchError((e) => {
        if (e.status === 404) {
          console.log(`Diagnosis with ID ${id} not found`);
          return from([null]);
        }
        console.error('Error retrieving diagnosis by ID:', e);
        return throwError(() => this.normalizeError(e));
      })
    );
  }

  /**
   * Update a diagnosis by ID
   * @param id Document ID
   * @param updatedData Updated diagnosis data
   * @returns Observable with update response
   */
  updateDiagnosis(id: string, updatedData: Partial<Diagnosis>): Observable<PouchDB.Core.Response> {
    return from(this.db.get<StoredDiagnosis>(id)).pipe(
      switchMap((doc) => {
        const updatedDoc: StoredDiagnosis = {
          ...doc,
          ...updatedData,
          _id: doc._id,
          _rev: doc._rev,
          type: 'diagnosis',
          createdAt: doc.createdAt // Keep original creation time
        };

        console.log('Updating diagnosis:', updatedDoc);
        return from(this.db.put(updatedDoc));
      }),
      tap((response) => {
        console.log('✅ Diagnosis updated successfully:', response);
      }),
      catchError((e) => {
        console.error('❌ Error updating diagnosis:', e);
        return throwError(() => this.normalizeError(e));
      })
    );
  }

  /**
   * Delete a diagnosis by ID
   * @param id Document ID
   * @returns Observable with delete response
   */
  deleteDiagnosis(id: string): Observable<PouchDB.Core.Response> {
    return from(this.db.get(id)).pipe(
      switchMap((doc: any) => from(this.db.remove(doc))),
      tap((response: any) => {
        console.log('✅ Diagnosis deleted successfully:', response);
      }),
      catchError((e) => {
        console.error('❌ Error deleting diagnosis:', e);
        return throwError(() => this.normalizeError(e));
      })
    );
  }

  /**
   * Get database info for debugging
   * @returns Observable with database info
   */
  getDatabaseInfo(): Observable<any> {
    return from(this.db.info()).pipe(
      tap((info) => {
        console.log('Database info:', info);
      }),
      catchError((e) => {
        console.error('Error getting database info:', e);
        return throwError(() => this.normalizeError(e));
      })
    );
  }

  private normalizeError(err: any): Error {
    if (err && typeof err === 'object') {
      if (err.reason) return new Error(err.reason);
      if (err.message) return new Error(err.message);
      if (err.error) return new Error(err.error);
    }
    return new Error(JSON.stringify(err));
  }
}
